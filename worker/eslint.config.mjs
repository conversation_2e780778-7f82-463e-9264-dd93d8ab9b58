import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
    { languageOptions: { globals: globals.node } },
    pluginJs.configs.recommended,
    ...tseslint.configs.recommended,
    {
        files: ['./src/**/*.ts'],
    },
    {
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'off',
        },
    },
    {
        ignores: [
            '**/dist/',
            'libs/pkw',
            'libs/mtt',
            'libs/rmtt',
            'libs/wptgo',
            'libs/friends',
            'src/local.ts',
        ],
    },
];
