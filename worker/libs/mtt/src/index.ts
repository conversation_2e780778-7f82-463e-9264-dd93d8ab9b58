import { UnrecoverableError } from 'bullmq';
import { MttConfigType, JobDataHandler } from 'shared';
import { scanAndFetch } from './scanning';
import { HttpApis } from './httpApis';
import { signupAndPlay } from './mtt';
import { commonProto } from './mtt/pb/commonProto';

export class MttMain {
    private storeTournamentDetails: (id: number, data: any) => void;
    private fetchTournamentDetails: (id: number) => Promise<any>;
    private urlConfig: MttConfigType;
    private proxyUrl: string | undefined;

    static init(
        storeTournamentDetails: (id: number, data: any) => void,
        fetchTournamentDetails: (id: number) => Promise<any>,
        urlConfig: MttConfigType,
        proxyUrl: string | undefined,
    ) {
        mtt.storeTournamentDetails = storeTournamentDetails;
        mtt.fetchTournamentDetails = fetchTournamentDetails;

        mtt.urlConfig = urlConfig;
        mtt.proxyUrl = proxyUrl;
    }

    static async run(
        token: string,
        action: string,
        tournamentId: number,
        ticketId: number,
        onMessage: JobDataHand<PERSON>,
        nickname: string,
        profileName: string | undefined,
    ): Promise<void | number[]> {
        switch (action) {
            case 'scan':
                return mtt.scan(token, onMessage);
            case 'play':
                return mtt.play(token, tournamentId, ticketId, onMessage, profileName);
            case 'check':
                return mtt.check(token, tournamentId, nickname, onMessage);
            case 'balance':
                return mtt.balance(token);
            default:
                throw new UnrecoverableError(`Unknown action: ${action}`);
        }
    }

    async scan(token: string, onMessage: JobDataHandler): Promise<void> {
        return scanAndFetch(
            token,
            onMessage,
            this.storeTournamentDetails,
            this.urlConfig.mttApi,
            this.proxyUrl,
        );
    }

    async play(
        token: string,
        tournamentId: number,
        ticketId: number,
        onMessage: JobDataHandler,
        profileName: string | undefined,
    ) {
        return signupAndPlay(
            token,
            tournamentId,
            ticketId,
            onMessage,
            this.fetchTournamentDetails,
            this.urlConfig,
            this.proxyUrl,
            profileName,
        );
    }

    async check(
        token: string,
        tournamentId: number,
        nickname: string,
        onMessage: JobDataHandler,
    ): Promise<void> {
        const httpApis = new HttpApis(this.proxyUrl, this.urlConfig.mttApi);

        const data = await httpApis.requestMttTournamentPlayers(token, tournamentId, nickname);
        if (data.ErrorCode) {
            throw new UnrecoverableError(`Tournament eligibility check error: ${data.ErrorCode}`);
        } else {
            onMessage({ registered: data.PlayersDetail?.length > 0 });
        }
    }

    async balance(token: string): Promise<number[]> {
        const httpApis = new HttpApis(this.proxyUrl, this.urlConfig.mttApi);
        const data = await httpApis.requestPlayerTicketsData(token);

        if (data) {
            return data.filter((ticket) => ticket.isUsable).map((ticket) => ticket.ticketId);
        }

        return [];
    }
}

const mtt = new MttMain();
