import { UnrecoverableError } from 'bullmq';

import {
    DelayService,
    GAME_PHASE,
    GameMode,
    JobDataHandler,
    logging,
    MttConfigType,
    UserStatus,
    GameState,
    GameType,
    StrategyResponseAction,
    sleep,
} from 'shared';

import { CONNECTION_STATUS } from './enums';
import { HttpApis } from './httpApis';
import { GamePlayer, WorldPlayer } from './player';
import { commonProto } from './mtt/pb/commonProto';
import { holdem } from './mtt/pb/holdem';
import { mttPro } from './mtt/pb/mtt';
import { processStrategy } from './strategy';

export async function signupAndPlay(
    token: string,
    mttId: number,
    ticketId: number,
    updateProgress: JobDataHandler,
    fetchTournamentDetails: Function,
    urlConfig: MttConfigType,
    proxyUrl?: string,
    profileName?: string,
): Promise<void> {
    logging.setTournamentId(mttId);

    let resolve, reject;
    const gamePromise = new Promise<void>((res, rej) => {
        resolve = res;
        reject = rej;
    });

    const onError = (error) => {
        reject(error);
        cleanup();
    };
    const finish = () => {
        resolve();
        cleanup();
    };

    const delayService = new DelayService(GameMode.NORMAL);
    const httpApis = new HttpApis(proxyUrl, urlConfig.mttApi);

    let tournamentConnectTimeout = null;
    let tournamentStatusTimeout = null;

    let dataManager = { token: token };

    let gamePlayer = new GamePlayer(dataManager, mttId, urlConfig, proxyUrl);
    let worldPlayer = new WorldPlayer(dataManager, mttId, ticketId, urlConfig, proxyUrl);

    let roomId = 0;
    let userId = -1;
    let seatNum = -1;

    let tournament: commonProto.IMttTournamentDetail = null;
    let payoutStructure = [];
    let tournamentPrizePool = 0;
    let tournamentPlayers = [];
    let tournamentPlayersSize = 0;

    let gameState: GameState;
    let currentLevel = 1;

    // Players that were forced to all-in
    let allinedPlayers = new Set();
    // Limit players actions when some actions are missing
    let limitedGameMode = false;

    let stats = null;

    let checkAndConnectToTournament = async (userId) => {
        const data = await httpApis.requestMttMultiTable(dataManager.token, userId);
        logging.info('checkAndConnectToTournament res', data);
        const isTournamentReady = data.UserGameInfo.some(
            (info: commonProto.IMtt_User_Current_Game_Info) =>
                info.SngMttLevelId === mttId &&
                (info.MttTournamentStatus > 0 || info.MttTournamentIsPreparing),
        );
        if (isTournamentReady) {
            clearInterval(tournamentConnectTimeout);
            gamePlayer.connect();
        }
        return isTournamentReady;
    };

    let onUserLoginResponse = async (newUserId: number, errorCode: commonProto.ErrorCode) => {
        logging.info('MTT: World player Logged in');
        userId = newUserId;
        if (errorCode) {
            reject(new Error(`WorldPlayer could not login: ${errorCode}`));
            return;
        }
        logging.setUserId(userId);

        clearInterval(tournamentStatusTimeout);
        tournamentStatusTimeout = setInterval(() => {
            if (seatNum >= 0 && gamePlayer.connectionStatus === CONNECTION_STATUS.LOGGED_IN) {
                updateProgress({ stats: stats, status: UserStatus.inGamePlay });
            } else {
                updateProgress({ stats: stats, status: UserStatus.waiting });
            }
        }, 5000);

        try {
            await updateTournamentDetails();

            if (tournament.Status >= commonProto.MTT_GAME_STATUS.STOP_SIGNUP) {
                const status = commonProto.MTT_GAME_STATUS[tournament.Status] ?? tournament.Status;
                throw new UnrecoverableError(`Tournament status ${status} does not allow joining`);
            }
            const response = await httpApis.requestJoinedTournaments(token);
            logging.info('requestJoinedTournaments res', response);

            const joinedTournament = response?.MttList?.find((t) => t.TournamentId === mttId);
            if (joinedTournament?.JoinStatus > 0) {
                logging.info(`Tournament ${mttId} already joined, status: ${joinedTournament.JoinStatus}`);
            } else if (joinedTournament?.JoinStatus < 0) {
                logging.info(`Reentering tournament ${mttId}, status: ${joinedTournament.JoinStatus}`);
                // there's also some MttTournamentDetail.MorebuyStatus field to check
                await worldPlayer.reenterTournament();
            } else {
                logging.info(`Signing up for tournament ${mttId}`);
                await worldPlayer.signup(onError);
            }
            clearInterval(tournamentConnectTimeout);
            if (!(await checkAndConnectToTournament(userId))) {
                tournamentConnectTimeout = setInterval(() => checkAndConnectToTournament(userId), 10_000);
            }
        } catch (error) {
            onError(error);
        }
    };

    const updateTournamentDetails = async (isRefresh = false) => {
        logging.info(`updateTournamentDetails: ${mttId} isRefresh: ${isRefresh}`);
        // feature: get table data

        const processTournamentDetails = (data: commonProto.IMttTournamentDetailResponse) => {
            if (!data) {
                throw new Error('Failed to fetch tournament details: no data');
            }
            if (!data?.TournamentDetail || data.ErrorCode) {
                logging.error(
                    `processTournamentDetails error: ${data.ErrorCode} - ${commonProto.ErrorCode[data.ErrorCode]}`,
                    data.ErrorCode,
                );
                throw new Error(
                    `Failed to fetch tournament details: ${commonProto.ErrorCode[data.ErrorCode] ?? data.ErrorCode}`
                );
            }
            tournament = data.TournamentDetail;
            if (tournament.PrizeMoney) {
                payoutStructure = tournament.PrizeMoney.map((p) => ({
                    position: p.Rank,
                    prize_value: parseInt(p.Money),
                })).filter((p) => p.prize_value > 0);
                tournamentPrizePool = payoutStructure.reduce((acc, p) => acc + p.prize_value, 0);
            } else {
                logging.warn('PrizeMoney not found in tournament details', data);
            }

            if (tournamentPlayers.length === 0) {
                tournamentPlayers = (tournament.PlayersDetail || []).map((player) => ({
                    stack: player.Coins,
                    rank: player.Rank || player.Index,
                }));
                tournamentPlayersSize = tournamentPlayers.length;

                const player = (tournament.PlayersDetail || []).find((player) => player.UserId === userId);
                if (player) {
                    stats = {
                        chips: player.Coins,
                        rank: player.Rank || player.Index,
                    };
                }
            }
        };

        try {
            let data = await fetchTournamentDetails(mttId);
            if (data != null) {
                logging.info(`Fetch tournament details successful processing: ${JSON.stringify(data).slice(0, 1000)}`);
            } else if (!isRefresh) {
                logging.info(`Fetch tournament details failed, making a request`);
                data = await httpApis.requestMttTournamentDetail(dataManager.token, mttId);
            }
            processTournamentDetails(data);
        } catch (error) {
            if (!isRefresh) {
                onError(error);
            } else {
                logging.error('Skipping error: requestMttTournamentDetail error', error);
            }
        }
    };

    let cleanup = () => {
        clearInterval(tournamentConnectTimeout);
        clearInterval(tournamentStatusTimeout);
        worldPlayer.disconnect();
        gamePlayer.disconnect();
    };

    const onUserTokenRes = (msg: holdem.IUserTokenRes) => {
        logging.info('MTT onUserTokenRes');
        gamePlayer.MTTEnterRoom(mttId);
        worldPlayer.disconnect();
    };

    const onEnterGameRes = (msg: mttPro.IMttEnterGameRes) => {
        if (msg.code) {
            logging.info(`EnterGame: ${mttPro.Code[msg.code] ?? msg.code}`);
            if (msg.code === mttPro.Code.Mtt_End || msg.code === mttPro.Code.NO_MTT_ROOM) {
                throw new UnrecoverableError('Tournament ended');
            }
        }
    };

    const onEnterRoomRes = (msg: holdem.IEnterRoomRes) => {
        if (mttId === msg.mttId) {
            roomId = msg.roomId;
            logging.setRoomId(roomId);
            gamePlayer.roomId = roomId;

            gameState = new GameState(
                `${mttId}:${roomId}:${userId}:${toMillis(tournament.StartingTime)}`,
                roomId.toString(),
                msg.bb,
                msg.ante,
                GameMode.NORMAL,
                GameType.NLHE,
            );
        }
    };

    const onRoomSnapshotMsg = (msg: holdem.IRoomSnapshotMsg) => {
        logging.info('onRoomSnapshotMsg', msg);
        if (mttId === msg.mttId) {
            const holeCards = Object.values(msg.holeCards).map(decodeCard);

            seatNum = -1;
            gameState.players = [];
            for (const playerData of msg.players) {
                const player = gameState.addPlayer(undefined, playerData.seatNum - 1, playerData.leftCoin);
                if (playerData.userId === userId) {
                    seatNum = playerData.seatNum;
                    player.hole_cards = holeCards.join('');
                }
            }

            gameState.setGameParams({
                bb_seat: msg.bbPos - 1,
                sb_seat: msg.sbPos - 1,
                dealer_seat: msg.dealerPos - 1,
            });

            checkAndFixSeats();

            gameState.actions.entries = [];
            limitedGameMode = !!(msg.boardCards || msg.currAct);
            if (limitedGameMode) {
                logging.info('Setting limited game mode for the current hand');
            }
        }
    };

    const onMttRoomSnapshotRes = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onMttRoomSnapshotRes', msg);

        currentLevel = msg.blindIndex;
        const blindsConfig = tournament.HoldemBlindsConfig[currentLevel - 1];
        gameState.setGameParams({
            big_blind: blindsConfig?.BigBlind,
            ante: blindsConfig?.Ante,
        });
    };

    const onNeedActionMsg = async (msg: holdem.INeedActionMsg) => {
        // If it's not current room - no need to act
        if (msg.roomId !== roomId) {
            return;
        }
        logging.info('[InGame] onNeedActionMsg', { msg });
        // add allined player's action if any
        // this covers the case when allined player is immediately after the bb
        if (allinedPlayers.size > 0) {
            const playerSeats = gameState.players.map((p) => p.seat_no);
            let playerSeatIdx = playerSeats.indexOf(msg.seatNum - 1);
            const prevSeat = playerSeats.at(playerSeatIdx - 1);
            addAllinedPlayerAction(prevSeat);
        }
        if (msg.seatNum === seatNum) {
            let stopWatch = new Date().getTime();
            let action =
                msg.optAction % 10 === holdem.Action.CHECK ? holdem.Action.CHECK : holdem.Action.FOLD;
            let amount: number;
            let mtt = mttData();
            let probability: number;

            if (!limitedGameMode) {
                const betProfileUserId = String(userId % 100);
                try {
                    let strategy = await gameState.fetchStrategy({
                        mtt,
                        profileName,
                        betProfileUserId,
                    });
                    const result = processStrategy(strategy, msg);
                    action = result.action;
                    amount = result.amount;
                    probability = result.probability;
                } catch (error) {
                    logging.error('fetchStrategy error', error, { gameState, mtt });
                }
            }

            const gameStage: GAME_PHASE = gameState.getGamePhase();

            // if the player is the first to make action, do not take the probability into account
            // there is no actions or the last action was a board card(s) action
            if (gameState.actions.entries.length === 0 || /^[A-Z0-9]/.test(gameState.actions.entries.at(-1).action)) {
                probability = undefined;
            }

            const calculatedDelayMs = delayService.calculateDelayMs(gameStage, action, probability);
            const timePassed = new Date().getTime() - stopWatch;
            const delayLeftMs = calculatedDelayMs - timePassed;
            logging
                .withTag('DELAY')
                .info(
                    `Calculated delay: ${calculatedDelayMs} (ms) for ${holdem.Action[action]} on ${gameStage}, delay left: ${delayLeftMs} ms`,
                );

            if (delayLeftMs > 0) {
                await sleep(delayLeftMs);
            }

            gamePlayer.Action(action, amount);
        }
    };

    const mttData = () => {
        if (!tournament) {
            return undefined;
        }

        let blind = tournament.HoldemBlindsConfig[currentLevel - 1];

        // check if tournament players contains current room players
        // if not add them with artificial ranks
        let playersStacks = gameState.players.map((player) => player.stack).sort((a, b) => b - a);

        // remove from stacks players that are already in tournament players
        let i = 0,
            j = 0;
        while (i < tournamentPlayers.length && j < playersStacks.length) {
            if (tournamentPlayers[i].stack === playersStacks[j]) {
                playersStacks.splice(j, 1);
                i++;
            } else if (tournamentPlayers[i].stack > playersStacks[j]) {
                i++;
            } else {
                j++;
            }
        }

        // create artificial players with stacks that are not in tournament players
        let artificialPlayers = playersStacks.map((stack, i) => ({
            stack: stack,
            rank: tournamentPlayers.length + i + 1,
        }));

        // sort concatenated players by stack and set ranks accordingly
        let players = tournamentPlayers
            .concat(artificialPlayers)
            .sort((a, b) => b.stack - a.stack)
            .map((player, i) => ({
                stack: player.stack,
                rank: i + 1,
            }))
            .filter((player) => player.stack != null && player.stack > 0);

        let bounty;
        if (tournament.TournamentMode != commonProto.TOURNAMENT_MODE.NORMAL) {
            const bountyTypes = {
                [commonProto.TOURNAMENT_MODE.HUNTER]: 'normal_knockout_ko',
                [commonProto.TOURNAMENT_MODE.SUPER_HUNTER]: 'progressive_bounty_pko',
                [commonProto.TOURNAMENT_MODE.Mystery]: 'mystery_bounty',
            };
            bounty = {
                initial_bounty: tournament.BountyFee,
                bounty_pot: tournament.BountyPot,
                bounty_proportion: tournament.HunterModeBountyProportion,
                progressive_bounty_increasing_rate: tournament.HunterModeBountyIncreasingRate,
                bounty_type: bountyTypes[tournament.TournamentMode],
            };
        }

        let prizePool = (tournament.GamePot ?? 0) + (tournament.BountyPot ?? 0);
        return {
            initial_chips: tournament.StartingCoins,
            prize_fee: tournament.PrizeFee,
            bounty_fee: tournament.BountyFee,
            registration_fee: (tournament.PrizeFee ?? 0) + (tournament.BountyFee ?? 0),
            total_entries: tournament.JoinedCount,
            prize_pool: Math.max(prizePool, tournamentPrizePool),
            payout_structure: payoutStructure,
            total_registered_players: Math.max(
                tournament.PlayersCount,
                tournamentPlayersSize,
                players.length,
            ),
            players: players,
            current_level: currentLevel,
            current_blind_level: currentLevel,
            blind_structure: [
                {
                    level: blind.Level,
                    ante: blind.Ante || 0,
                    small_blind: blind.SmallBlind,
                    big_blind: blind.BigBlind,
                },
            ],
            bounty_structure: bounty,
        };
    };

    const onPlayerActionMsg = (msg: holdem.IPlayerActionMsg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onPlayerActionMsg', msg);

        const actionWithAmount = (
            msg.action === holdem.Action.BET ||
            msg.action === holdem.Action.RAISE ||
            msg.action === holdem.Action.ALL_IN
        );
        const amount = actionWithAmount ? msg.deskCoin : undefined;
        const action = {
            [holdem.Action.CHECK]: StrategyResponseAction.CHECK,
            [holdem.Action.CALL]: StrategyResponseAction.CALL,
            [holdem.Action.BET]: StrategyResponseAction.BET,
            [holdem.Action.FOLD]: StrategyResponseAction.FOLD,
            [holdem.Action.RAISE]: StrategyResponseAction.RAISE,
            [holdem.Action.ALL_IN]: StrategyResponseAction.ALLIN,
        }[msg.action]

        gameState.addAction(action, amount, msg.seatNum - 1);

        // add allined player's action if any
        // this covers most of the cases
        addAllinedPlayerAction(msg.seatNum % gameState.players.length);
    };

    const addAllinedPlayerAction = (seat: number) => {
        if (allinedPlayers.has(seat)) {
            logging.info(`adding allin action for seat ${seat}`);
            gameState.addAction(StrategyResponseAction.ALLIN, undefined, seat);
            allinedPlayers.delete(seat);
        }
    };

    const onDealerPosMsg = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.resetRoundValues();
        logging.info('[InGame] onDealerPosMsg', msg);

        gameState.setGameParams({
            dealer_seat: msg.dealerPos - 1,
            sb_seat: msg.sbPos - 1,
            bb_seat: msg.bbPos - 1,
            big_blind: tournament.HoldemBlindsConfig[currentLevel - 1]?.BigBlind,
            ante: tournament.HoldemBlindsConfig[currentLevel - 1]?.Ante,
        });

        gameState.players = msg.seats.map((seat) => ({
            seat_no: seat.seatNum - 1,
            stack: (seat.leftCoin || 0) + (seat.deskCoin || 0) + gameState.ante,
        }));
        checkAndFixSeats();
        gameState.actions.entries = [];
        allinedPlayers = new Set();
        limitedGameMode = false;
        // get fresh tournament data before each round
        gamePlayer.requestTournamentInfo();
    };

    const checkAndFixSeats = () => {
        if (!gameState.getPlayerBySeatNo(gameState.sb_seat)) {
            gameState.sb_seat = -1;
        }
        if (!gameState.getPlayerBySeatNo(gameState.dealer_seat)) {
            gameState.dealer_seat = -1;
        }
    };

    const onHoleCardsMsg = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onHoleCardsMsg', msg);

        const currentPlayer = gameState.getPlayerBySeatNo(seatNum - 1);
        if (currentPlayer) {
            const cards = Array.from(msg.cards).map(decodeCard);
            currentPlayer.hole_cards = cards.join('');
        }
    };

    const onBoardCardsMsg = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onBoardCardsMsg', msg);

        const cards = Array.from(msg.cards).map(decodeCard);
        gameState.addCardsAction(cards.join(''))
    };

    const onPlayerStateMsg = (msg: holdem.IPlayerStateMsg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onPlayerStateMsg', msg);
        if (msg.state === holdem.Action.ALL_IN) {
            const seat = msg.seatNum - 1;
            const player = gameState.getPlayerBySeatNo(seat);
            if (seat !== gameState.sb_seat && seat !== gameState.bb_seat && player?.stack > gameState.ante) {
                allinedPlayers.add(seat);
            }
        }
    };

    const onRiseBlindNotifyMsg = (msg) => {
        logging.info('onRiseBlindNotifyMsg', msg);
        if (roomId === msg.roomId) {
            currentLevel = msg.riseIndex;
        }
    };

    const onLeaveRoomRes = (msg) => {
        if (roomId === msg.roomId) {
            logging.info('LeaveRoomRes', msg);
        }
    };

    const onRoundResultMsg = (msg) => {
        if (roomId === msg.roomId) {
            logging.info('RoundResultMsg', msg);
        }
    };

    const onRewardMsg = (msg) => {
        if (mttId === msg.mttId && userId === msg.userId) {
            logging.info('RewardMsg', msg);
            updateProgress({ stats: { chips: 0, rank: msg.rank }, status: UserStatus.inGamePlay });
            finish();
        }
    };

    const onMttRealTimeRecordRes = (msg: mttPro.IMttRealTimeRecordRes) => {
        if (mttId === msg.mttId) {
            tournamentPlayers = msg.players.map((player) => ({
                stack: player.leftcoin,
                rank: player.rank,
            }));
            tournamentPlayersSize = msg.AllPlayerCount;

            if (msg.AllPlayerCount !== tournament.PlayersCount) {
                updateTournamentDetails(true);
            }

            const player = msg.players.find((player) => player.userId === userId);
            if (player) {
                stats = {
                    chips: player.leftcoin,
                    rank: player.rank,
                };
            } else {
                // if player is not in the list, compute stats from the last known state
                stats = {
                    chips: gameState.getPlayerBySeatNo(seatNum - 1)?.stack,
                    rank: msg.curPlayer.rank,
                };
            }
        }
    };

    const onSeatOccupiedMsg = (msg) => {
        logging.info('onSeatOccupiedMsg', msg);
        if (msg.roomId == roomId && msg.userId == userId) {
            seatNum = msg.seatNum;
        }
    };

    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.UserTokenRes, onUserTokenRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.MttEnterGameRes, onEnterGameRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.EnterRoomRes, onEnterRoomRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RoomSnapshotMsg, onRoomSnapshotMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.MttRoomSnapshotRes, onMttRoomSnapshotRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.NeedActionMsg, onNeedActionMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerActionMsg, onPlayerActionMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.DealerPosMsg, onDealerPosMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.HoleCardsMsg, onHoleCardsMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.BoardCardsMsg, onBoardCardsMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerStateMsg, onPlayerStateMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RiseBlindNotifyMsg, onRiseBlindNotifyMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.LeaveRoomRes, onLeaveRoomRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RoundResultMsg, onRoundResultMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.SeatOccupiedMsg, onSeatOccupiedMsg);
    gamePlayer.websocket.addMessageHandler(mttPro.MessageId.RewardMsg, onRewardMsg);

    gamePlayer.onTournamentInfo = onMttRealTimeRecordRes;
    gamePlayer.onConnectionFailed = onError;

    worldPlayer.onLoggedIn = onUserLoginResponse;
    worldPlayer.onConnectionFailed = onError;
    worldPlayer.connect();

    return gamePromise;
}

const decodeCard = (card) => {
    let number = { 10: 'T', 11: 'J', 12: 'Q', 13: 'K', 14: 'A' }[card % 16] || card % 16;
    let suit = { 1: 'd', 2: 's', 4: 'h', 8: 'c' }[card >> 4];
    return number + suit;
};

const toMillis = (timestamp) => {
    if (timestamp instanceof Date) {
        return timestamp.getTime();
    } else if (timestamp && typeof timestamp.seconds === 'number' && typeof timestamp.nanos === 'number') {
        return timestamp.seconds * 1000 + timestamp.nanos / 1000000;
    } else {
        return 0;
    }
};
