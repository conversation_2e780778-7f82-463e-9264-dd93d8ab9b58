import { mock, test } from 'node:test';
import assert from 'node:assert';
import { MttMain } from '../src/index';
import { HttpApis } from '../src/httpApis';
import { MttConfigType } from 'shared';

const mockConfig: MttConfigType = {
    mttWorld: 'ws://test-world.com',
    mttGame: 'ws://test-game.com',
    mttApi: 'https://test-api.com',
};

test('MttMain - balance method - successful response with usable tickets', async () => {
    MttMain.init(
        () => {}, // storeTournamentDetails
        () => Promise.resolve({}), // fetchTournamentDetails
        mockConfig,
        undefined, // proxyUrl
    );

    const mockTicketsData = [
        { ticketId: 1, isUsable: true },
        { ticketId: 2, isUsable: false },
        { ticketId: 3, isUsable: true },
        { ticketId: 4, isUsable: true },
        { ticketId: 5, isUsable: false },
    ];

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(mockTicketsData));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.run('test-token', 'balance', 0, 0, () => {}, 'test-nickname', undefined);

    assert.deepStrictEqual(result, [1, 3, 4]); // Only usable ticket IDs
    assert.strictEqual(mockRequestPlayerTicketsData.mock.calls.length, 1);
});

test('MttMain - balance method - empty tickets response', async () => {
    MttMain.init(
        () => {},
        () => Promise.resolve({}),
        mockConfig,
        undefined,
    );

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve([]));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.run('test-token', 'balance', 0, 0, () => {}, 'test-nickname', undefined);

    assert.deepStrictEqual(result, []);
});

test('MttMain - balance method - null response from API', async () => {
    MttMain.init(
        () => {},
        () => Promise.resolve({}),
        mockConfig,
        undefined,
    );

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(null));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.run('test-token', 'balance', 0, 0, () => {}, 'test-nickname', undefined);

    assert.deepStrictEqual(result, []);
});

test('MttMain - balance method - all tickets unusable', async () => {
    MttMain.init(
        () => {},
        () => Promise.resolve({}),
        mockConfig,
        undefined,
    );

    const mockTicketsData = [
        { ticketId: 1, isUsable: false },
        { ticketId: 2, isUsable: false },
        { ticketId: 3, isUsable: false },
    ];

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(mockTicketsData));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await MttMain.run('test-token', 'balance', 0, 0, () => {}, 'test-nickname', undefined);

    assert.deepStrictEqual(result, []);
});

test('MttMain - balance method - direct instance method call', async () => {
    const mttInstance = new MttMain();

    (mttInstance as any).urlConfig = {
        mttWorld: 'ws://test-world.com',
        mttGame: 'ws://test-game.com',
        mttApi: 'https://test-api.com',
    };
    (mttInstance as any).proxyUrl = undefined;

    const mockTicketsData = [{ ticketId: 42, isUsable: true }];

    const mockRequestPlayerTicketsData = mock.fn(() => Promise.resolve(mockTicketsData));
    mock.method(HttpApis.prototype, 'requestPlayerTicketsData', mockRequestPlayerTicketsData);

    const result = await mttInstance.balance('test-token');

    assert.deepStrictEqual(result, [42]);
});
