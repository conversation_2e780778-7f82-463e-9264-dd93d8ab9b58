import path from 'path';
import protobuf from 'protobufjs';

import {
    DelayService,
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    logging,
    MttUserData,
    PkwUserData,
    PkwAuthData,
    RoomStartParams,
} from 'shared';

import { pb as world_pb } from '../proto/ws_protocol';

import cv from './cv';
import { domainManager } from './network/DomainManager';
import pkwGame from './pkwGame';
import pkwRoom, { LobbyDataFunction } from './pkwRoom';
import { ecdhHandler } from './tools/ecdhHandler';
import EncryptUtils from '../encryptUtils';
import { ServerErrorCodes } from '../serverErrorCodes';

export const PkwMain = {
    async initScan(proxyUrl?: string) {
        logging.info('PkwMain.initScan');
        cv.initCV(proxyUrl);
        pkwRoom.init();
        await loadProto();
    },

    async init(
        proxyUrl?: string,
        gameId?: world_pb.GameId,
        gameMode: GameMode = GameMode.NORMAL,
        gameTypeCode: GameType = GameType.NLHE,
    ) {
        cv.initCV(proxyUrl);
        pkwRoom.init();

        pkwGame.init();
        pkwGame.setGameModeCode(GameMode.NORMAL); // Should be NORMAL for all gameModes, even SPLASH, ZOOM and BOMB
        pkwGame.setDelayService(new DelayService(gameMode));

        await loadProto();

        pkwRoom.setGameId(gameId);
        pkwRoom.setGameTypeCode(gameTypeCode);
    },

    pkwScan(
        loginData: PkwUserData,
        jobType: JobType,
        onMessage: LobbyDataFunction,
    ): Promise<void> {
        logging.withTag('SDK').info('pkwScan');
        pkwRoom.setLobbyDataCb(onMessage);

        return startPkwGame(loginData, jobType);
    },

    async pkwPlay(
        loginData: PkwUserData,
        jobType: JobType,
        joinRoomId: number,
        roomParams: RoomStartParams,
        onMessage: JobDataHandler,
    ): Promise<void> {
        logging.withTag('SDK').info('pkwPlay');

        pkwGame.setUpdateProgressCb(onMessage);
        pkwGame.setProfileName(roomParams.profileName);
        pkwGame.setExtraTimeEnabled(roomParams.extraTimeEnabled);

        pkwRoom.setJoinRoomId(joinRoomId);
        pkwRoom.setRoomParams(roomParams);

        return startPkwGame(loginData, jobType);
    },

    async pkwLogin(loginData: PkwUserData, jobType: JobType): Promise<MttUserData> {
        logging.withTag('SDK').info('pkwLogin');
        return new Promise((resolve) => {
            pkwRoom.setLoginServerCB(resolve);
            startPkwGame(loginData, jobType);
        });
    },

    async finishGame() {
        await pkwRoom.leaveRoom();
    },

    closeWebSocket() {
        logging.withTag('WEBSOCKET').info('PKW - Deliberately closing websocket connection...');

        cv.netWork.disconnect();
    },
};

async function startPkwGame(
    loginData: PkwUserData,
    jobType: JobType,
): Promise<void> {
    setLoginUserData(loginData.pkwAuthData, loginData.user.nickname);
    cv.dataHandler.getUserData().deviceInfo = `{"disroot":false,"dmodel":"","dname":"wefans","duuid":"${loginData.deviceId}","dversion":""}`;
    encodeMd5token();
    setDomainData(loginData.pkwAuthData);

    let resolveGame, rejectGame;
    const gamePromise = new Promise<void>((resolve, reject) => {
        resolveGame = resolve;
        rejectGame = reject;
    });

    pkwRoom.setPkwUserData(loginData.pkwAuthData.uid, jobType);
    pkwRoom.setErrorCb((error: any) => {
        if (error instanceof Error) {
            rejectGame(error);
        } else {
            rejectGame(new Error(ServerErrorCodes[error] || error));
        }
    });
    pkwRoom.setSuccessCb(resolveGame);

    ecdhHandler.getInstance().ecdh_init();
    cv.netWorkManager.startGame();

    return gamePromise;
}

function setLoginUserData(pkwAuthData: PkwAuthData, nickname: string) {
    const userData = cv.dataHandler.getUserData();

    userData.user_ip = pkwAuthData.appIP;
    userData.user_token = pkwAuthData.token;
    userData.u32Uid = pkwAuthData.uid;
    userData.user_id = pkwAuthData.uid.toString();
    userData.nick_name = nickname;
    userData.file_upload_url = pkwAuthData.pkw_file_addr;
    if (pkwAuthData.client_type) {
        cv.config.SET_CLIENT_TYPE(pkwAuthData.client_type);
    }
}

function setDomainData(pkwAuthData: PkwAuthData) {
    pkwAuthData.gate_addr.forEach((gateAddr: string) => {
        domainManager.addDomain({
            h5: gateAddr,
        });
    });
}

async function loadProto(): Promise<void> {
    (protobuf.parse as any).defaults.keepCase = true;

    const vPBInfo = [
        { path: path.dirname(__dirname) + '/proto/ws_protocol.proto', type: 'worldPB' },
        { path: path.dirname(__dirname) + '/proto/gs_protocol.proto', type: 'gamePB' },
        { path: path.dirname(__dirname) + '/proto/gate.proto', type: 'gate' },
        { path: path.dirname(__dirname) + '/proto/data.proto', type: 'data' },
    ];

    const loadPromises = vPBInfo.map(async ({ path, type }) => {
        try {
            const source = await protobuf.load(path);
            switch (type) {
                case 'worldPB':
                    cv.worldPB = source;
                    break;
                case 'gamePB':
                    cv.gamePB = source;
                    break;
                case 'gate':
                    cv.gatePB = source;
                    break;
                case 'data':
                    cv.dataPB = source;
                    break;
                default:
                    break;
            }
        } catch (error: any) {
            logging.error('protobuf load error', error, { path });
            throw error;
        }
    });
    await Promise.all(loadPromises);
}

function encodeMd5token() {
    const token = cv.dataHandler.getUserData().user_token;
    cv.dataHandler.getUserData().user_token = EncryptUtils.MD5(EncryptUtils.MD5(token));
}
