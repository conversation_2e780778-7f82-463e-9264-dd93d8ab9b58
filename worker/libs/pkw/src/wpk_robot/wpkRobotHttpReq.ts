import fetch from 'node-fetch';

import EncryptUtils from '../encryptUtils';
import querystring from 'querystring';
import { logging, LoginError, PlatformUserData, UserData, randomStr } from 'shared';
import { HttpsProxyAgent } from 'https-proxy-agent';

const WPK_HEADERS = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'User-Agent':
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
};

const WPK_HTTP_URL = process.env.WPK_URL;

export class RobotHttpReq {
    static STATIC_SALT = '49C764D98E177F70';

    static async wpkRequest(
        user: PlatformUserData,
        uri: string,
        param: any,
        signed = false,
        proxyUrl = null,
        queryParams = null,
    ) {
        const userId = user.userId;
        const QUERY_PARAMS = {
            userId: user.userId,
            sessionToken: user.token,
            version: '********',
            lang: 'en',
            deviceType: 1,
            deviceId: user.deviceId,
            idfa: 0,
            platform: 'unknown',
            channel: 0,
        };
        Object.assign(QUERY_PARAMS, queryParams);
        let url = `${WPK_HTTP_URL}${uri}?${querystring.stringify(QUERY_PARAMS)}`;
        if (queryParams) {
            logging.info('[RobotHttpReq] wpkRequest additional query params:', { queryParams });
        }
        if (signed) {
            url = RobotHttpReq.signUrl(url);
        }
        logging.info('[RobotHttpReq] wpkRequest: ', { userId, url, param });

        const rawResponse = await fetch(url, {
            method: 'POST',
            body: JSON.stringify(param),
            headers: WPK_HEADERS,
            agent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined,
            signal: AbortSignal.timeout(20_000),
        });

        if (!rawResponse.ok) {
            const errorText = await rawResponse.text();
            logging.error('[RobotHttpReq] wpkRequest error', null, { userId, uri, errorText });
            throw new Error(`wpkRequest failed: ${errorText}`);
        }

        const response: any = await rawResponse.json();

        if (typeof response !== 'object') {
            logging.warn('[RobotHttpReq] wpkRequest response is not an object', {
                userId,
                uri,
                response,
            });
            throw new Error('Response is not an object');
        }
        if ([2000, 2001].includes(response.errorCode)) {
            throw new LoginError(`${response.errorCode}: ${response.errMsg}`);
        }
        if (response.errorCode != 0) {
            logging.error('[RobotHttpReq] wpkRequest error', response.errorCode, { response, userId, uri });
        } else {
            const { errorCode: _, ...responseWithoutError } = response;
            logging.info('[RobotHttpReq] wpkRequest success', {
                userId,
                uri,
                response: responseWithoutError,
            });
        }
        return response;
    }

    static signUrl(url: string) {
        // take all query params, sort them, and generate a signature
        const urlObj = new URL(url);
        const params = urlObj.searchParams;

        params.sort();

        let signParams = [];
        for (let [key, value] of params.entries()) {
            signParams.push(`${key}${value}`);
        }

        let signText = signParams.join(',');
        // Clear all non-alphanumeric characters
        signText = signText.replace(/\W/g, '');
        const salt = '110';
        // Add before and after a salt
        signText = `${salt}${signText}${salt}`;

        let signature = EncryptUtils.MD5(signText).toString().toUpperCase();

        return url + `&sign=${signature}`;
    };

    static async wpkLogin(user: UserData, phoneLogin = false, signed = false, proxyUrl = null) {
        const enAesKey = await RobotHttpReq.generateUserAESKey({
            userId: user.userId,
            token: '0',
            deviceId: user.deviceId,
        });
        const username = user.username;
        const password = EncryptUtils.MD5(user.password + RobotHttpReq.STATIC_SALT).toUpperCase()
        logging.info(`[RobotHttpReq] wpkLogin: ${WPK_HTTP_URL}`);
        const loginParams = phoneLogin
            ? `phoneNum=${user.phoneNum}&countryCode=${encodeURIComponent(user.countryCode)}`
            : `account=${encodeURIComponent(username)}&countryCode=%2B86`;

        let url = `${WPK_HTTP_URL}/user/phone_login?${loginParams}&password=${password}&time=*************&lang=en&isSimulator=false&deviceVersion=&phoneModel=safari&networkOper=&appVersionCode=1000000&version=********&signVersion=&packageName=&deviceType=1&deviceId=${user.deviceId}&idfa=0&platform=unknown&channel=0&isAutoLogin=true`;
        if (signed) {
            url = RobotHttpReq.signUrl(url);
        }

        const rawResponse = await fetch(url, {
            method: 'POST',
            body: JSON.stringify({ aesKey: enAesKey }),
            headers: WPK_HEADERS,
            agent: proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined,
            signal: AbortSignal.timeout(20_000),
        });
        const response: any = await rawResponse.json();

        if (!response || response.errorCode != 0) {
            throw new LoginError(`${response?.errorCode}: ${response?.errMsg}`);
        }

        if (response.sessionToken == '0') {
            response.sessionToken = await RobotHttpReq.verifyDevice({
                userId: response.user.userId,
                token: response.sessionToken,
                deviceId: user.deviceId,
            });
        }
        return response;
    }

    static async generateUserAESKey(user: PlatformUserData) {
        const rasPubKeyResp = await RobotHttpReq.wpkRequest(user, '/system/getUserRASPubKey.anon', {});
        const rasPubKey = rasPubKeyResp?.data;
        if (!rasPubKey) {
            return undefined;
        }
        return EncryptUtils.encryptRSA(randomStr(6), rasPubKey);
    };

    static async verifyDevice(user: PlatformUserData): Promise<string> {
        const response = await RobotHttpReq.wpkRequest(user, '/user/verify_update_device_security_code', {
            updateType: 1,
            code: 1234,
        });
        if (response?.data?.token) {
            return response.data.token;
        }
        logging.error(`verifyDevice request failed`, response);
        throw new Error(`verifyDevice request failed with error ${response?.errorCode}: ${response?.errMsg}`);
    }
}
