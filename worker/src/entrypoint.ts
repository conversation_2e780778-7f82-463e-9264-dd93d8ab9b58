import { GamePlatformAdapter, ManagingPlatformAdapter, TransferJobData } from './models/model';
import { JobDataHandler, JobType, JobData, logging, LoginError, UserData } from 'shared';

export class PlatformAwareGameEntrypoint {
    private platformAdapter: ManagingPlatformAdapter;
    private gameAdapter: GamePlatformAdapter;

    constructor(platformAdapter: ManagingPlatformAdapter, gameAdapter: GamePlatformAdapter) {
        this.platformAdapter = platformAdapter;
        this.gameAdapter = gameAdapter;
    }

    async init(proxyUrl?: string): Promise<void> {
        this.platformAdapter.init(proxyUrl);
        await this.gameAdapter.init(proxyUrl);
    }

    async start(
        user: UserData,
        action: JobType,
        tableId: number,
        params: JobData,
        onMessage: JobDataHandler,
    ): Promise<void> {
        const platformUser = await this.platformAdapter.login(user);
        try {
            switch (action) {
                case JobType.CHECK:
                case JobType.SCAN:
                case JobType.PLAY: {
                    let userData: any = platformUser;
                    if (this.gameAdapter.isMtt()) {
                        userData = await this.platformAdapter.fetchMttUserData(platformUser);
                    }
                    await this.gameAdapter.start(userData, action, tableId, params, onMessage);
                    break;
                }
                case JobType.TRANSFER:
                    await this.platformAdapter.transfer(platformUser, params as TransferJobData);
                    break;
                case JobType.BALANCE: {
                    const balance = await this.platformAdapter.balance(platformUser);

                    if (this.gameAdapter.isMtt()) {
                        const userData = await this.platformAdapter.fetchMttUserData(platformUser);

                        balance.tickets = (await this.gameAdapter.start(
                            userData,
                            action,
                            tableId,
                            params,
                            onMessage,
                        )) as number[];
                    }

                    logging.info('Balance', JSON.stringify(balance));

                    onMessage({ balance });
                    break;
                }
                default:
                    return Promise.reject(new Error(`Unsupported action: ${action}`));
            }
        } catch (e) {
            logging.error(`Error during action '${action}' for userId ${user.userId}`, e);
            if (e instanceof LoginError) {
                logging.info(`Login error detected, clearing login cache for userId ${user.userId}`);
                await this.platformAdapter.clearCache(user.userId);
            }
            throw e;
        }
    }

    async stop(action: JobType): Promise<void> {
        await this.gameAdapter.stop(action);
    }
}
