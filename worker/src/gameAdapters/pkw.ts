import { UnrecoverableError } from 'bullmq';
import {
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    JobData,
    logging,
    RoomStartParams,
    PkwUserData,
    TableData,
} from 'shared';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';
import { pkwConfig } from './pkwConfig';

class PkwAdapter implements GamePlatformAdapter {
    constructor(
        private readonly gameId: number = 2,
        private readonly gameMode: GameMode = GameMode.NORMAL,
        private readonly gameType: GameType = GameType.NLHE,
        private readonly defaultParams: {
            extraTimeEnabled?: boolean;
        } = {},
    ) {}

    async init(proxyUrl?: string): Promise<void> {
        await PkwMain.init(proxyUrl, this.gameId, this.gameMode, this.gameType);
    }

    async start(
        userData: PkwUserData,
        jobType: JobType,
        tableId: number,
        params: JobD<PERSON>,
        onMessage: JobDataHandler,
    ): Promise<void> {
        if (jobType !== JobType.SCAN && jobType !== JobType.PLAY) {
            throw new UnrecoverableError(`Unsupported game platform action: ${jobType}`);
        }

        if (jobType === JobType.SCAN) {
            const { gameMode, currencies } = pkwConfig[params.appId];
            const tablesDataHandler = (tables: TableData[]) => {
                tables = tables.filter((table) => {
                    return (
                        table.gameId === this.gameId &&
                        table.gameMode === gameMode &&
                        currencies.includes(table.currency)
                    );
                });
                onMessage({ tables: tables.map((t) => ({ ...t, appId: params.appId })) });
            };
            return PkwMain.pkwScan(userData, jobType, tablesDataHandler);
        }

        const botParams: RoomStartParams = {
            buyInMultiplier: params.buyInMultiplier ?? 100,
            rebuyEnabled: params.rebuyEnabled ?? true,
            rebuyThreshold: params.rebuyThreshold ?? 50,
            maxRebuyCount: params.maxRebuyCount ?? 100,
            withdrawAmount: params.withdrawAmount ?? 0,
            withdrawThreshold: params.withdrawThreshold ?? 0,
            profileName: params.profileName,
            extraTimeEnabled: this.defaultParams.extraTimeEnabled ?? false,
        };

        return PkwMain.pkwPlay(userData, jobType, tableId, botParams, onMessage);
    }

    async stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Adapter] stop', jobType);
        if (jobType == JobType.PLAY) {
            await PkwMain.finishGame();
        }
    }

    isMtt(): boolean {
        return false;
    }
}

export const cashAdapter = new PkwAdapter(2, GameMode.NORMAL, GameType.NLHE, {
    extraTimeEnabled: true,
});
export const diamondAdapter = new PkwAdapter(2, GameMode.NORMAL, GameType.NLHE, {
    extraTimeEnabled: true,
});
export const splashAdapter = new PkwAdapter(60, GameMode.SPLASH);
export const splashDiamondAdapter = new PkwAdapter(60, GameMode.SPLASH);
export const zoomAdapter = new PkwAdapter(40, GameMode.ZOOM);
export const shortDeckAdapter = new PkwAdapter(2, GameMode.NORMAL, GameType.SHORTDECK);
