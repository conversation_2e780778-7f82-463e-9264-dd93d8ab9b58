import { <PERSON><PERSON><PERSON>, <PERSON>Data, JobDataHandler, MttUserData, Balance, UserData, PlatformUserData } from 'shared';
import { CurrencyType } from '../types';

export interface TransferJobData extends JobData {
    currency: CurrencyType;
    transferAmount: number; // amount to transfer to receiver
    receiverId: number; // userId of the receiver for transfer
    receiverUsername?: string; // username of the receiver for transfer
}

export interface GamePlatformAdapter {
    init(proxyUrl?: string): Promise<void>;
    start(
        user: any,
        action: JobType,
        tableId: number,
        params: JobData,
        onMessage: JobDataHandler,
    ): Promise<void | number[]>;
    stop(action: JobType): Promise<void>;
    isMtt(): boolean;
}

export interface ManagingPlatformAdapter {
    init: (proxyUrl?: string) => void;
    login: (user: UserData) => Promise<PlatformUserData>;
    fetchMttUserData: (platformUser: PlatformUserData) => Promise<MttUserData>;
    balance: (platformUser: PlatformUserData) => Promise<Balance>;
    transfer: (platformUser: PlatformUserData, params: TransferJobData) => Promise<void>;
    clearCache: (userId: number) => Promise<void>;
}
