import { UnrecoverableError } from 'bullmq';
import { GamePlatformAdapter, ManagingPlatformAdapter } from './models/model';
import process from 'node:process';
import { getUserAccount, initMongoDBConnection } from './mongo';
import redis from './redis';
import { version } from '../package.json';
import Redlock from 'redlock';
import { parentPort, workerData } from 'node:worker_threads';
import {
    featureFlagValue,
    logging,
    JobType,
    JobUpdateData,
    PlayerStatus,
    PlatformType,
    UserData,
    AppType,
    WorkerData,
    sleep,
} from 'shared';
import { PlatformAwareGameEntrypoint } from './entrypoint';

const redlock = new Redlock([redis.connection], {
    retryCount: -1,
    retryDelay: 200,
    retryJitter: 50,
});

const getUserData = async (playerId: string | undefined): Promise<UserData> => {
    const userInformation = await getUserAccount(playerId);
    if (!userInformation) {
        throw new UnrecoverableError(`User not found: ${playerId}`);
    }

    return {
        ...userInformation,
        countryCode: userInformation.countryCode ?? '',
        phoneNum: userInformation.phoneNum ?? '',
        platformId: userInformation.platformId ?? PlatformType.WPK,
    };
};

const loadGameAdapter = async (appId: AppType): Promise<GamePlatformAdapter> => {
    switch (appId) {
        case AppType.CASH:
            return (await import('./gameAdapters/pkw')).cashAdapter;
        case AppType.DIAMOND:
            return (await import('./gameAdapters/pkw')).diamondAdapter;
        case AppType.MTT:
            return (await import('./gameAdapters/mtt')).mttAdapter;
        case AppType.R4:
            return (await import('./gameAdapters/rmtt')).RMttAdapter;
        case AppType.SPLASH:
            return (await import('./gameAdapters/pkw')).splashAdapter;
        case AppType.SPLASH_DIAMOND:
            return (await import('./gameAdapters/pkw')).splashDiamondAdapter;
        case AppType.ZOOM:
            return (await import('./gameAdapters/pkw')).zoomAdapter;
        case AppType.SHORTDECK:
            return (await import('./gameAdapters/pkw')).shortDeckAdapter;
        case AppType.FRIENDS:
            return (await import('./gameAdapters/friends')).FriendsAdapter;
        default:
            throw new UnrecoverableError(`Unknown app type: ${appId}`);
    }
};

const loadPlatform = async (platformId: PlatformType): Promise<ManagingPlatformAdapter> => {
    switch (platformId) {
        case PlatformType.RWPK:
            return (await import('./platformsAdapters/rwpk')).rwpkPlatform;
        case PlatformType.WPK:
            return (await import('./platformsAdapters/wpk')).wpkPlatform;
        case PlatformType.WPTGO:
            return (await import('./platformsAdapters/wptgo')).wptgoPlatform;
        default:
            throw new UnrecoverableError(`Unknown platform type: ${platformId}`);
    }
};

function updateProgress(data: JobUpdateData) {
    const dataString = JSON.stringify(data);
    logging
        .withTag('JOB_PROGRESS')
        .info('Updating job progress', dataString.length > 1000 ? `${dataString.slice(0, 1000)}...` : data);

    parentPort?.postMessage({
        updatedAt: Date.now(),
        data,
    });
}

(async () => {
    const job = workerData as WorkerData;

    const jobType = job.name;
    if (!Object.values(JobType).includes(jobType)) {
        throw new UnrecoverableError(`Unknown job type: ${jobType}`);
    }
    const jobData = job.data;

    let gameAdapter: GamePlatformAdapter;
    if (jobType === JobType.SCAN && jobData.appIds && jobData.appIds?.length > 0) {
        gameAdapter = (await import('./gameAdapters/pkwScan')).pkwScanAdapter;
    } else {
        gameAdapter = await loadGameAdapter(jobData.appId);
    }

    await initMongoDBConnection();
    const userData: UserData = await getUserData(jobData.playerId);

    const platformAdapter = await loadPlatform(userData.platformId);

    const entrypoint = new PlatformAwareGameEntrypoint(platformAdapter, gameAdapter);

    logging
        .withTag('JOB_PROGRESS')
        .info(`Starting ${AppType[jobData.appId]} - ${PlatformType[userData.platformId]} adapter`);

    // acquire lock to prevent multiple workers from starting at the same time
    const lockKey = `worker-start-job-${jobData.appId}-${jobType}`;
    const defaultDurations = {
        [`worker-start-job-${AppType.MTT}-${JobType.PLAY}`]: 2000,
        [`worker-start-job-${AppType.MTT}-${JobType.CHECK}`]: 1000,
    };
    const lockDuration = await featureFlagValue(
        'worker-start-job-lock-duration',
        { appId: jobData.appId, jobType },
        defaultDurations[lockKey],
    );
    if (lockDuration) {
        await redlock.acquire([lockKey], lockDuration);
    }

    // eslint-disable-next-line no-async-promise-executor
    const exitCode: number = await new Promise(async (resolve, reject) => {
        const jobMonitor = setInterval(async () => {
            const updatedJob = await redis.workerQueue.getJob(job.id!);
            if (updatedJob?.data?.shouldStop) {
                clearInterval(jobMonitor);
                logging.withTag('JOB_PROGRESS').info(`Exiting job: ${jobType} ID: ${job.id}`, {
                    reason: updatedJob.data.stopReason,
                    delay: updatedJob.data.stopDelay,
                });

                updateProgress({ status: PlayerStatus.STOPPING });

                if (updatedJob.data.stopDelay && updatedJob.data.stopDelay > 0) {
                    logging
                        .withTag('JOB_PROGRESS')
                        .info(`Delaying job stop for ${updatedJob.data.stopDelay}s`);
                    await sleep(updatedJob.data.stopDelay * 1000);
                }

                setTimeout(() => {
                    logging.withTag('JOB_PROGRESS').warn(`Forcing job exit after timeout ID: ${job.id}`);
                    resolve(0);
                }, 300000);

                await entrypoint.stop(jobType);
                updateProgress({ status: PlayerStatus.STOPPED });
                resolve(0);
            }
        }, 5000);

        parentPort?.on('message', async (msg) => {
            // close scan job only, and let other jobs to finish
            if (msg?.type === 'closing' && jobType === JobType.SCAN) {
                logging.withTag('JOB_PROGRESS').info(`Closing job: ${jobType} ID: ${job.id}`);
                clearInterval(jobMonitor);
                await entrypoint.stop(jobType);
                resolve(0);
            }
        });

        logging.init({
            jobId: job.id,
            platform: jobData.appId,
            version,
        });

        await entrypoint.init(jobData.proxyUrl);

        updateProgress({ status: PlayerStatus.INITIALIZED });

        const tableId = Number(jobData.tableId ?? -1);
        if (isNaN(tableId)) {
            throw new UnrecoverableError(`Invalid tableId: ${jobData.tableId}`);
        }
        logging
            .withTag('JOB_PROGRESS')
            .info(
                `Starting job: ${jobType} ID: ${job.id} for user: ${userData.username} on worker version: ${version}`,
            );
        try {
            await entrypoint.start(userData, jobType, tableId, { ...userData, ...jobData }, updateProgress);
            resolve(0);
        } catch (error) {
            updateProgress({ status: PlayerStatus.ERROR });
            reject(error);
        }
    });

    process.exit(exitCode);
})();
